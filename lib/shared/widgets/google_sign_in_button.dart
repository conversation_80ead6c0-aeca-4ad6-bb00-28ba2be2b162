import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:flutter/material.dart';

/// Google Sign-In button widget following Material Design guidelines
class GoogleSignInButton extends StatelessWidget {
  /// Creates a Google Sign-In button.
  const GoogleSignInButton({
    required this.onPressed,
    this.isLoading = false,
    super.key,
  });

  /// Callback function when the button is pressed.
  final VoidCallback? onPressed;

  /// Whether the button is in loading state.
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: AppDimensions.buttonHeight,
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.white,
          side: BorderSide(
            color: Colors.grey.shade300,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          elevation: 0,
          shadowColor: Colors.transparent,
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.textSecondary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Google logo (using icon for now)
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: Colors.grey.shade300,
                        width: 0.5,
                      ),
                    ),
                    child: const Icon(
                      Icons.g_mobiledata,
                      color: Colors.blue,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  const Text(
                    'Continue with Google',
                    style: AppTextStyles.button,
                  ),
                ],
              ),
      ),
    );
  }
}
