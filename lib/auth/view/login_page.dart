import 'package:bloomg_flutter/auth/cubit/login_cubit.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/models/password.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/navigation/auth_navigation.dart';
import 'package:bloomg_flutter/shared/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocProvider(
        create: (_) => LoginCubit(getIt<AuthRepository>()),
        child: const LoginForm(),
      ),
    );
  }
}

class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _emailController.addListener(() {
      context.read<LoginCubit>().emailChanged(_emailController.text);
    });
    _passwordController.addListener(() {
      context.read<LoginCubit>().passwordChanged(_passwordController.text);
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoginCubit, LoginState>(
      listener: (context, state) {
        if (state.status.isSubmissionFailure) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Authentication Failure'),
                backgroundColor: AppColors.error,
              ),
            );
        }
        if (state.status.isSubmissionSuccess) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              const SnackBar(
                content: Text('Login successful!'),
                backgroundColor: AppColors.success,
              ),
            );
        }
      },
      child: SafeArea(
        child: SingleChildScrollView(
          padding:
              const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  const SizedBox(height: AppDimensions.spacingHuge),
                  const BloomgLogo(),
                  const SizedBox(height: AppDimensions.spacingMassive),
                  // Login Form
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Login',
                          style: AppTextStyles.heading1,
                        ),
                        const SizedBox(height: AppDimensions.spacingXXXL),
                        // Login Form Container
                        AuthFormContainer(
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Email Field
                                BlocBuilder<LoginCubit, LoginState>(
                                  buildWhen: (previous, current) =>
                                      previous.email != current.email ||
                                      previous.emailTouched !=
                                          current.emailTouched,
                                  builder: (context, state) {
                                    return ValidatedAuthFormField(
                                      label: 'Your Email Address',
                                      controller: _emailController,
                                      keyboardType: TextInputType.emailAddress,
                                      onFocusChange: ({
                                        required bool hasFocus,
                                      }) {
                                        if (hasFocus) {
                                          context
                                              .read<LoginCubit>()
                                              .emailTouched();
                                        }
                                      },
                                      errorText: state.email.error?.message,
                                      showError: state.emailTouched &&
                                          state.email.isNotValid,
                                    );
                                  },
                                ),
                                const SizedBox(height: AppDimensions.spacingXL),
                                // Password Field
                                BlocBuilder<LoginCubit, LoginState>(
                                  buildWhen: (previous, current) =>
                                      previous.password != current.password ||
                                      previous.passwordTouched !=
                                          current.passwordTouched,
                                  builder: (context, state) {
                                    return ValidatedPasswordField(
                                      label: 'Your Password',
                                      controller: _passwordController,
                                      onFocusChange: ({
                                        required bool hasFocus,
                                      }) {
                                        if (hasFocus) {
                                          context
                                              .read<LoginCubit>()
                                              .passwordTouched();
                                        }
                                      },
                                      errorText: state.password.error?.message,
                                      showError: state.passwordTouched &&
                                          state.password.isNotValid,
                                    );
                                  },
                                ),
                                const SizedBox(
                                  height: AppDimensions.spacingXXL,
                                ),
                                // Login Button
                                BlocBuilder<LoginCubit, LoginState>(
                                  buildWhen: (previous, current) =>
                                      previous.status != current.status,
                                  builder: (context, state) {
                                    return AuthButton(
                                      text: 'Log in',
                                      isLoading:
                                          state.status.isSubmissionInProgress,
                                      onPressed: state.status.isValidated
                                          ? () => context
                                              .read<LoginCubit>()
                                              .logInWithCredentials()
                                          : null,
                                    );
                                  },
                                ),
                                const SizedBox(height: AppDimensions.spacingXL),
                                // Forgot Password Link
                                Center(
                                  child: TextButton(
                                    onPressed: () =>
                                        AuthNavigation.toForgotPassword(
                                      context,
                                    ),
                                    child: const Text(
                                      'Forgot password?',
                                      style: AppTextStyles.linkPlain,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: AppDimensions.spacingXL),
                                // Divider
                                Row(
                                  children: [
                                    const Expanded(
                                      child: Divider(
                                        color: AppColors.textSecondary,
                                        thickness: 1,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: AppDimensions.spacingM,
                                      ),
                                      child: Text(
                                        'or',
                                        style: AppTextStyles.bodySmall.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                    ),
                                    const Expanded(
                                      child: Divider(
                                        color: AppColors.textSecondary,
                                        thickness: 1,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: AppDimensions.spacingXL),
                                // Google Sign-In Button
                                BlocBuilder<LoginCubit, LoginState>(
                                  buildWhen: (previous, current) =>
                                      previous.status != current.status,
                                  builder: (context, state) {
                                    return GoogleSignInButton(
                                      isLoading:
                                          state.status.isSubmissionInProgress,
                                      onPressed: () => context
                                          .read<LoginCubit>()
                                          .logInWithGoogle(),
                                    );
                                  },
                                ),
                                const SizedBox(height: AppDimensions.spacingM),
                                // Apple Sign-In Button
                                BlocBuilder<LoginCubit, LoginState>(
                                  buildWhen: (previous, current) =>
                                      previous.status != current.status,
                                  builder: (context, state) {
                                    return AppleSignInButton(
                                      isLoading:
                                          state.status.isSubmissionInProgress,
                                      onPressed: () {
                                        // TODO(auth): Implement Apple Sign-In
                                        // authentication logic
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                              'Apple Sign-In coming soon!',
                                            ),
                                            backgroundColor: AppColors.primary,
                                          ),
                                        );
                                      },
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Spacer(),
                        // Create Account Link
                        Column(
                          children: [
                            TextButton(
                              onPressed: () =>
                                  AuthNavigation.toCreateAccount(context),
                              child: const Text(
                                "Don't have an account? Create one",
                                style: AppTextStyles.link,
                              ),
                            ),
                            const SizedBox(height: AppDimensions.spacingXXXL),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
